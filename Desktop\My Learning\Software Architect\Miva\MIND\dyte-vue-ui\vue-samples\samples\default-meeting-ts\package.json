{"name": "default-meeting-ts", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"@dytesdk/vue-ui-kit": "^3.0.1", "@dytesdk/web-core": "^2.4.4", "vue": "^3.5.13"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.1", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^7.0.2", "typescript": "~5.8.3", "vite": "^6.2.6", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}